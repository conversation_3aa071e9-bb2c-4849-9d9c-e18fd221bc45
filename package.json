{"name": "ai-credit-match-web", "version": "0.0.1", "description": "AI信贷匹配助手", "author": "FisherX", "type": "module", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite", "build:prod": "vite build", "build:test": "vite build --mode test", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix", "format": "prettier --write src/"}, "repository": {"type": "git", "url": "https://gitee.com/sdjkcn/ai-credit-match-web.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.3.0", "axios": "1.9.0", "clipboard": "2.0.11", "echarts": "5.6.0", "element-plus": "2.9.9", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "3.0.2", "splitpanes": "4.0.4", "vue": "3.5.16", "vue-cropper": "1.1.1", "vue-router": "4.5.1", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "sass-embedded": "1.89.1", "unplugin-auto-import": "0.18.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "@eslint/js": "^9.17.0", "eslint": "^9.17.0", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.14.0"}, "overrides": {"quill": "2.0.2"}}