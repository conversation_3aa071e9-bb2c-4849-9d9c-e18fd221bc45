<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行机构" prop="bankName">
        <el-input
          v-model="queryParams.bankName"
          placeholder="请输入银行机构"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select
          v-model="queryParams.productType"
          placeholder="产品类型"
          clearable
          style="width: 200px"
        >
          <el-option label="个人贷款" value="personal" />
          <el-option label="企业贷款" value="enterprise" />
          <el-option label="信用卡" value="credit_card" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 200px">
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="80" />
      <el-table-column
        label="产品名称"
        align="center"
        prop="productName"
        :show-overflow-tooltip="true"
        width="200"
      />
      <el-table-column label="银行机构" align="center" prop="bankName" width="150" />
      <el-table-column label="产品类型" align="center" prop="productType" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.productType === 'personal'" type="primary">个人贷款</el-tag>
          <el-tag v-else-if="scope.row.productType === 'enterprise'" type="success"
            >企业贷款</el-tag
          >
          <el-tag v-else-if="scope.row.productType === 'credit_card'" type="warning">信用卡</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="额度范围" align="center" prop="amountRange" width="150" />
      <el-table-column label="利率范围" align="center" prop="interestRate" width="120" />
      <el-table-column label="期限" align="center" prop="termRange" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改产品对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <el-form ref="productRef" :model="form" :rules="rules" label-width="100px">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="productName">
                  <el-input v-model="form.productName" placeholder="请输入产品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="银行机构" prop="bankName">
                  <el-input v-model="form.bankName" placeholder="请输入银行机构" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="产品类型" prop="productType">
                  <el-select
                    v-model="form.productType"
                    placeholder="请选择产品类型"
                    style="width: 100%"
                  >
                    <el-option label="个人贷款" value="personal" />
                    <el-option label="企业贷款" value="enterprise" />
                    <el-option label="信用卡" value="credit_card" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态">
                  <el-radio-group v-model="form.status">
                    <el-radio :value="1">启用</el-radio>
                    <el-radio :value="0">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 产品要素 -->
          <el-tab-pane label="产品要素" name="features">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="额度范围" prop="amountRange">
                  <el-input v-model="form.amountRange" placeholder="如：1万-100万" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="利率范围" prop="interestRate">
                  <el-input v-model="form.interestRate" placeholder="如：年化3.45%-18%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="期限" prop="termRange">
                  <el-input v-model="form.termRange" placeholder="如：最长5年" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="还款方式" prop="repaymentMethod">
                  <el-input v-model="form.repaymentMethod" placeholder="如：等额本息、先息后本" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 准入要求 -->
          <el-tab-pane label="准入要求" name="requirements">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="年龄要求" prop="ageRequirement">
                  <el-input v-model="form.ageRequirement" placeholder="如：22-59周岁" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="收入要求" prop="incomeRequirement">
                  <el-input v-model="form.incomeRequirement" placeholder="如：月收入5000元以上" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="职业要求" prop="occupationRequirement">
                  <el-input
                    v-model="form.occupationRequirement"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入职业要求"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="其他要求" prop="otherRequirements">
                  <el-input
                    v-model="form.otherRequirements"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入其他要求"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 征信要求 -->
          <el-tab-pane label="征信要求" name="credit">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="征信查询限制" prop="creditInquiryLimit">
                  <el-input
                    v-model="form.creditInquiryLimit"
                    placeholder="如：近1个月≤3次，近3个月≤5次"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="逾期要求" prop="overdueRequirement">
                  <el-input
                    v-model="form.overdueRequirement"
                    placeholder="如：近2年无4个1，近2年无2"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="征信详细要求" prop="creditDetails">
                  <el-input
                    v-model="form.creditDetails"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入征信详细要求"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 申请流程 -->
          <el-tab-pane label="申请流程" name="process">
            <el-form-item label="申请流程" prop="applicationProcess">
              <el-input
                v-model="form.applicationProcess"
                type="textarea"
                :rows="5"
                placeholder="请输入申请流程，如：步骤一：河农农行行 →网点客户经理提交申请资料..."
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 产品简介 -->
          <el-tab-pane label="产品简介" name="description">
            <el-form-item label="产品简介" prop="productDescription">
              <el-input
                v-model="form.productDescription"
                type="textarea"
                :rows="8"
                placeholder="请输入产品简介"
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 准入区域 -->
          <el-tab-pane label="准入区域" name="region">
            <el-form-item label="准入区域" prop="serviceRegion">
              <el-input
                v-model="form.serviceRegion"
                type="textarea"
                :rows="3"
                placeholder="如：全国、北京、上海等"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看产品详情对话框 -->
    <el-dialog title="产品详情" v-model="viewOpen" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="产品名称">{{ viewForm.productName }}</el-descriptions-item>
        <el-descriptions-item label="银行机构">{{ viewForm.bankName }}</el-descriptions-item>
        <el-descriptions-item label="产品类型">
          <el-tag v-if="viewForm.productType === 'personal'" type="primary">个人贷款</el-tag>
          <el-tag v-else-if="viewForm.productType === 'enterprise'" type="success">企业贷款</el-tag>
          <el-tag v-else-if="viewForm.productType === 'credit_card'" type="warning">信用卡</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewForm.status === 1 ? 'success' : 'danger'">
            {{ viewForm.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="额度范围">{{ viewForm.amountRange }}</el-descriptions-item>
        <el-descriptions-item label="利率范围">{{ viewForm.interestRate }}</el-descriptions-item>
        <el-descriptions-item label="期限">{{ viewForm.termRange }}</el-descriptions-item>
        <el-descriptions-item label="还款方式">{{ viewForm.repaymentMethod }}</el-descriptions-item>
        <el-descriptions-item label="年龄要求">{{ viewForm.ageRequirement }}</el-descriptions-item>
        <el-descriptions-item label="收入要求">{{
          viewForm.incomeRequirement
        }}</el-descriptions-item>
        <el-descriptions-item label="征信查询限制" :span="2">{{
          viewForm.creditInquiryLimit
        }}</el-descriptions-item>
        <el-descriptions-item label="逾期要求" :span="2">{{
          viewForm.overdueRequirement
        }}</el-descriptions-item>
        <el-descriptions-item label="职业要求" :span="2">{{
          viewForm.occupationRequirement
        }}</el-descriptions-item>
        <el-descriptions-item label="其他要求" :span="2">{{
          viewForm.otherRequirements
        }}</el-descriptions-item>
        <el-descriptions-item label="征信详细要求" :span="2">{{
          viewForm.creditDetails
        }}</el-descriptions-item>
        <el-descriptions-item label="申请流程" :span="2">
          <div style="white-space: pre-line">{{ viewForm.applicationProcess }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="产品简介" :span="2">
          <div style="white-space: pre-line">{{ viewForm.productDescription }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="准入区域" :span="2">{{
          viewForm.serviceRegion
        }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="Product">
import { parseTime } from '@/utils/ruoyi'

const { proxy } = getCurrentInstance()

const productList = ref([])
const open = ref(false)
const viewOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const activeTab = ref('basic')

const data = reactive({
  form: {},
  viewForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productName: undefined,
    bankName: undefined,
    productType: undefined,
    status: undefined
  },
  rules: {
    productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
    bankName: [{ required: true, message: '银行机构不能为空', trigger: 'blur' }],
    productType: [{ required: true, message: '产品类型不能为空', trigger: 'change' }]
  }
})

const { queryParams, form, viewForm, rules } = toRefs(data)

// 模拟数据
const mockData = [
  {
    id: 1,
    productName: '农业银行-集团e贷',
    bankName: '中国农业银行',
    productType: 'personal',
    amountRange: '最高100万元(一般30万内)',
    interestRate: '年化利率最低至3.45%',
    termRange: '最长5年',
    repaymentMethod: '3年先息后本，3-5等额本息；提前还款无违约金',
    ageRequirement: '22-59周岁',
    incomeRequirement: '月收入5000元以上',
    occupationRequirement:
      '党政机关、财政统发工资事业单位、如公办学校老师、公办医院医生等(月收入可放宽至3500元以上)；分支机构遍布全国并实施垂直管理的系统性法人企业：烟草、电力、电信、石油、邮政、铁路、银行、保险公司等优质央企、国企等；其他优质行业单位',
    otherRequirements: '其他优质行业单位',
    creditInquiryLimit: '近1个月≤3次，近3个月≤5次',
    overdueRequirement: '近2年无4个1，近2年无2',
    creditDetails:
      '信用卡和贷款无呆账、坏账、止付、冻结、核销等状态；查询：查询可沟通，网贷多也可以申请(正常查询要求：查询：近1个月≤3次，近3个月≤5次)',
    applicationProcess:
      '步骤一：河农农行行 →网点客户经理提交申请资料\n步骤二：审批通过后，登录农行掌银"贷款" → "网捷贷"即可签约\n步骤三：成功签约后，自主支用、还款',
    productDescription:
      '集团e贷是中国农业银行为优质集团单位的正式员工提供的一种纯信用线上消费贷款。\n这种贷款方式，员工可以根据自己的单位职级和工资收入获得相应的额度，具有良好的灵活性、随借随还的特点。集团e贷的额度最高可达100万元，按日计息，并且通常执行优惠利率，利率可能低至年化3.45%。申请过程相对简单，审批和线上提款，自主最快1个工作日通过地满足客户的需求。',
    serviceRegion: '全国准入区域：全国',
    status: 1,
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    productName: '建设银行-快贷',
    bankName: '中国建设银行',
    productType: 'personal',
    amountRange: '最高30万元',
    interestRate: '年化利率4.35%-7.2%',
    termRange: '最长1年',
    repaymentMethod: '随借随还',
    ageRequirement: '18-60周岁',
    incomeRequirement: '有稳定收入来源',
    occupationRequirement: '建行代发工资客户、公积金客户、按揭客户等',
    otherRequirements: '建行存量优质客户',
    creditInquiryLimit: '近6个月查询不超过6次',
    overdueRequirement: '近2年内无连续逾期3期记录',
    creditDetails: '征信良好，无不良记录',
    applicationProcess:
      '步骤一：登录建行手机银行\n步骤二：选择快贷产品申请\n步骤三：系统自动审批\n步骤四：审批通过即可使用',
    productDescription:
      '建行快贷是建设银行推出的个人客户全流程线上自助贷款，客户可通过建行手机银行在线完成贷款申请、审批、签约、支用、还款等操作。',
    serviceRegion: '全国',
    status: 1,
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: 3,
    productName: '工商银行-融e借',
    bankName: '中国工商银行',
    productType: 'personal',
    amountRange: '最高80万元',
    interestRate: '年化利率3.7%-18%',
    termRange: '最长5年',
    repaymentMethod: '等额本息、等额本金',
    ageRequirement: '18-65周岁',
    incomeRequirement: '月收入3000元以上',
    occupationRequirement: '工行代发工资客户、星级客户',
    otherRequirements: '工行优质客户',
    creditInquiryLimit: '近3个月查询不超过5次',
    overdueRequirement: '近2年无逾期记录',
    creditDetails: '个人征信良好，无不良信用记录',
    applicationProcess:
      '步骤一：登录工行手机银行或网上银行\n步骤二：选择融e借产品\n步骤三：填写申请信息\n步骤四：等待审批结果\n步骤五：审批通过后签约放款',
    productDescription:
      '融e借是工商银行向符合特定条件的借款人发放的，用于个人合法合规消费用途的无担保无抵押人民币贷款。',
    serviceRegion: '全国',
    status: 1,
    createTime: '2024-01-17 09:15:00'
  }
]

/** 查询产品列表 */
function getList() {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    let filteredData = [...mockData]

    // 模拟筛选
    if (queryParams.value.productName) {
      filteredData = filteredData.filter(item =>
        item.productName.includes(queryParams.value.productName)
      )
    }
    if (queryParams.value.bankName) {
      filteredData = filteredData.filter(item => item.bankName.includes(queryParams.value.bankName))
    }
    if (queryParams.value.productType) {
      filteredData = filteredData.filter(item => item.productType === queryParams.value.productType)
    }
    if (queryParams.value.status !== undefined && queryParams.value.status !== '') {
      filteredData = filteredData.filter(item => item.status === parseInt(queryParams.value.status))
    }

    // 模拟分页
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize
    const end = start + queryParams.value.pageSize

    productList.value = filteredData.slice(start, end)
    total.value = filteredData.length
    loading.value = false
  }, 500)
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    productName: undefined,
    bankName: undefined,
    productType: undefined,
    amountRange: undefined,
    interestRate: undefined,
    termRange: undefined,
    repaymentMethod: undefined,
    ageRequirement: undefined,
    incomeRequirement: undefined,
    occupationRequirement: undefined,
    otherRequirements: undefined,
    creditInquiryLimit: undefined,
    overdueRequirement: undefined,
    creditDetails: undefined,
    applicationProcess: undefined,
    productDescription: undefined,
    serviceRegion: undefined,
    status: 1
  }
  activeTab.value = 'basic'
  proxy?.resetForm('productRef')
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy?.resetForm('queryRef')
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加产品'
}

/** 查看按钮操作 */
function handleView(row) {
  viewForm.value = { ...row }
  viewOpen.value = true
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset()
  const productId = row?.id || ids.value[0]
  // 模拟获取产品详情
  const product = mockData.find(item => item.id === productId)
  if (product) {
    form.value = { ...product }
    open.value = true
    title.value = '修改产品'
  }
}

/** 提交按钮 */
function submitForm() {
  proxy?.$refs['productRef'].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        // 模拟更新
        const index = mockData.findIndex(item => item.id === form.value.id)
        if (index !== -1) {
          mockData[index] = { ...form.value }
          proxy?.$modal.msgSuccess('修改成功')
          open.value = false
          getList()
        }
      } else {
        // 模拟新增
        const newProduct = {
          ...form.value,
          id: mockData.length + 1,
          createTime: new Date()
            .toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })
            .replace(/\//g, '-')
        }
        mockData.push(newProduct)
        proxy?.$modal.msgSuccess('新增成功')
        open.value = false
        getList()
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const productIds = row?.id ? [row.id] : ids.value
  const productNames = row?.productName
    ? [row.productName]
    : mockData.filter(item => productIds.includes(item.id)).map(item => item.productName)

  proxy?.$modal
    .confirm('是否确认删除产品"' + productNames.join('、') + '"？')
    .then(function () {
      // 模拟删除
      productIds.forEach(id => {
        const index = mockData.findIndex(item => item.id === id)
        if (index !== -1) {
          mockData.splice(index, 1)
        }
      })
      proxy?.$modal.msgSuccess('删除成功')
      getList()
    })
    .catch(() => {})
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? '启用' : '禁用'
  proxy?.$modal
    .confirm('确认要"' + text + '""' + row.productName + '"产品吗?')
    .then(function () {
      // 模拟状态更新
      const index = mockData.findIndex(item => item.id === row.id)
      if (index !== -1) {
        mockData[index].status = row.status
      }
      proxy?.$modal.msgSuccess(text + '成功')
    })
    .catch(function () {
      row.status = row.status === 1 ? 0 : 1
    })
}

// 页面加载时获取列表
onMounted(() => {
  getList()
})
</script>
