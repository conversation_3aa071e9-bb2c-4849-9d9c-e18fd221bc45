import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import prettier from 'eslint-plugin-prettier'
import configPrettier from 'eslint-config-prettier'
import globals from 'globals'
import { readFileSync } from 'fs'

// 动态读取 auto-import 生成的全局变量
let autoImportGlobals = {}
try {
  const autoImportContent = readFileSync('./.eslintrc-auto-import.json', 'utf8')
  autoImportGlobals = JSON.parse(autoImportContent)
} catch (error) {
  console.warn('Auto-import globals file not found, skipping...')
}

export default [
  js.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  configPrettier,
  {
    files: ['**/*.{js,mjs,cjs,vue}'],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...autoImportGlobals.globals,
        defineEmits: 'readonly',
        defineProps: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly'
      }
    },
    plugins: {
      prettier
    },
    rules: {
      'prettier/prettier': 'error',
      'vue/multi-word-component-names': 'off',
      'no-unused-vars': 'warn',
      'no-console': 'warn'
    }
  }
]
